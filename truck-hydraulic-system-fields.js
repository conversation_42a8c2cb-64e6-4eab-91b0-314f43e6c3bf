// Show only the first field initially, then reveal each subsequent field only after the previous one is filled out
function getFirstVisibleFieldIndex() {
    return componentAttributes.findIndex(f => f.IsVisible);
}

export function getVisibleFields(formData) {
    // Always show the first visible field, then reveal each next after previous is filled
    const firstIdx = getFirstVisibleFieldIndex();
    let visibleCount = 0;
    for (let i = 0; i < componentAttributes.length; i++) {
        const field = componentAttributes[i];
        if (!field.IsVisible) continue;
        if (i === firstIdx || (formData[field.Name] !== undefined && formData[field.Name] !== null && formData[field.Name] !== "")) {
            visibleCount++;
        } else {
            break;
        }
    }
    let count = 0;
    return componentAttributes.map((field, i) => {
        if (!field.IsVisible) return false;
        if (count < visibleCount) {
            count++;
            return true;
        }
        return false;
    });
}