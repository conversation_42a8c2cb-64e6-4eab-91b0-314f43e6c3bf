/* Modern, clean form styles for SYS.700CPK */
body { font-family: 'Segoe UI', Arial, sans-serif; background: #f7f7f7; margin: 0; }
main { max-width: 600px; margin: 2rem auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #0001; padding: 2rem; }
h1 { text-align: center; color: #2a3a4a; }
form { display: flex; flex-direction: column; gap: 1.2rem; }
.form-group { display: flex; flex-direction: column; }
label { font-weight: 500; margin-bottom: 0.3rem; }
input[type="text"], input[type="number"], select { padding: 0.5rem; border: 1px solid #bbb; border-radius: 4px; font-size: 1rem; }
input[type="radio"], input[type="checkbox"] { margin-right: 0.5rem; }
button[type="submit"] { background: #2a7ae2; color: #fff; border: none; border-radius: 4px; padding: 0.7rem 1.5rem; font-size: 1.1rem; cursor: pointer; transition: background 0.2s; }
button[type="submit"]:hover { background: #1a5bb8; }
