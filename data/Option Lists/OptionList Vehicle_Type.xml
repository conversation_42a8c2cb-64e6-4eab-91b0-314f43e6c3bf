<OptionList Name="Vehicle_Type" Description="Vehicle Type" ImageSubFolder="">
  <Property Name="Value" DataType="String" DefaultValue="" Sequence="1" />
  <Property Name="Description" DataType="String" DefaultValue="" Sequence="2" />
  <Property Name="Order" DataType="Number" DefaultValue="1" Sequence="3" />
  <Property Name="Visible" DataType="Boolean" DefaultValue="True" Sequence="4" />
  <Property Name="Locked" DataType="Boolean" DefaultValue="False" Sequence="5" />
  <Property Name="Tooltip" DataType="String" DefaultValue="" Sequence="6" />
  <Property Name="ImageLink" DataType="String" DefaultValue="" Sequence="7" />
  <Property Name="Comments" DataType="String" DefaultValue="" Sequence="8" />
  <Group Name="3" />
  <Group Name="34ton" />
  <Group Name="CLASS4" />
  <Group Name="NOOT" />
  <Group Name="NOVAN" />
  <Group Name="VAN" />
  <Value>
    <Property Name="Value">OT</Property>
    <Property Name="Description">1 1/2 Ton &amp; Larger Trucks</Property>
    <Property Name="Order">2</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments"></Property>
    <Group Name="3" />
    <Group Name="CLASS4" />
    <Group Name="NOVAN" />
  </Value>
  <Value>
    <Property Name="Value">VN</Property>
    <Property Name="Description">Van</Property>
    <Property Name="Order">3</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments"></Property>
    <Group Name="3" />
    <Group Name="NOOT" />
    <Group Name="VAN" />
  </Value>
  <Value>
    <Property Name="Value">PU</Property>
    <Property Name="Description">3/4 &amp; 1 Ton Trucks</Property>
    <Property Name="Order">1</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments"></Property>
    <Group Name="3" />
    <Group Name="34ton" />
    <Group Name="NOOT" />
    <Group Name="NOVAN" />
  </Value>
  <Tags />
</OptionList>