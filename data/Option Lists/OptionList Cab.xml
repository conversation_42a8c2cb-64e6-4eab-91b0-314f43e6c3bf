<OptionList Name="Cab" Description="Cab Question" ImageSubFolder="">
  <Property Name="Value" DataType="String" DefaultValue="" Sequence="1" />
  <Property Name="Description" DataType="String" DefaultValue="" Sequence="2" />
  <Property Name="Order" DataType="Number" DefaultValue="1" Sequence="3" />
  <Property Name="Visible" DataType="Boolean" DefaultValue="True" Sequence="4" />
  <Property Name="Locked" DataType="Boolean" DefaultValue="False" Sequence="5" />
  <Property Name="Tooltip" DataType="String" DefaultValue="" Sequence="6" />
  <Property Name="ImageLink" DataType="String" DefaultValue="" Sequence="7" />
  <Property Name="Comments" DataType="String" DefaultValue="" Sequence="8" />
  <Value>
    <Property Name="Value">CRW</Property>
    <Property Name="Description">Crew Cab</Property>
    <Property Name="Order">3</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments"></Property>
  </Value>
  <Value>
    <Property Name="Value">EXT</Property>
    <Property Name="Description">Extended Cab</Property>
    <Property Name="Order">2</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments"></Property>
  </Value>
  <Value>
    <Property Name="Value">STD</Property>
    <Property Name="Description">Standard Cab</Property>
    <Property Name="Order">1</Property>
    <Property Name="Visible">True</Property>
    <Property Name="Locked">False</Property>
    <Property Name="Tooltip"></Property>
    <Property Name="ImageLink"></Property>
    <Property Name="Comments"></Property>
  </Value>
  <Tags />
</OptionList>