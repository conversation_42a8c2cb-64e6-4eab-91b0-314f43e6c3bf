# Harper Configurator Prototypes

## Overview

This project contains two modular, modern web-based prototypes for truck hydraulic system configuration:

- **SYS.700CPK Configurator**: A general-purpose truck hydraulic system configurator.
- **SYS.Butler Configurator**: A specialized configurator for Butler truck beds and accessories.

Each configurator is implemented as a standalone HTML file with its own JavaScript and CSS for modularity and ease of extension.

---

## Details of Each Configurator

### SYS.700CPK Configurator

- **File:** `sys-700cpk.html`
- **Purpose:** General truck hydraulic system configuration.
- **Attributes:**
  - Application, Year, Vehicle Type, Make, Engine, Pump, Kit, Clutch, Adapter, Product, Cab, Chassis, Reservoir, Hoses660, Valve, ACC Hose, Aux Hoses, Control, Harness, AGKitLessMiniPack, ValveYN, Macfit.
- **UI Elements:**
  - Typeable dropdowns, radio buttons, checkboxes, and textboxes.
- **How it works:**
  - Users select or enter values for each attribute. Option lists are provided for many fields. The form is modular and can be extended to fetch real data.

### SYS.Butler Configurator

- **File:** `sys-butler.html`
- **Purpose:** Specialized for Butler truck beds and accessories.
- **Attributes:**
  - Arms, Axle, BallType, Bed, BedFeature, BedType, CabAxle, ControlType, Headache, HydPowerFlat, HydPwrUnit, ISFord, LHToolbox, Make, MntTabs, MountKit, Outlets, RHToolbox, RubRails, SideRails, Skirted, Spikes, SpinLength, Tailboard, TruckConfig, Wireharness, WorkLights, XBed, Years, ButlerAdapter, and more.
- **UI Elements:**
  - Typeable dropdowns, radio buttons, checkboxes, and numeric textboxes.
- **How it works:**
  - Users select or enter values for each attribute. Option lists are provided for many fields. The form is modular and can be extended to fetch real data.

---

## How to Run the Configurators

1. **Start a local web server** (Python 3 required):

   ```bash
   python -m http.server
   ```

2. **Open your browser** and navigate to:
   - [http://localhost:8000/sys-700cpk.html](http://localhost:8000/sys-700cpk.html) for the SYS.700CPK Configurator
   - [http://localhost:8000/sys-butler.html](http://localhost:8000/sys-butler.html) for the SYS.Butler Configurator

---

## Customization

- Option lists are currently static for demonstration. You can extend the JavaScript files to fetch real data or integrate with your backend.
- The forms are modular and styled for clarity and usability.

---

For questions or further customization, contact the project maintainer.
