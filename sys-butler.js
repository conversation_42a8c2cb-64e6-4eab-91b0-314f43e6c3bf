import { componentAttributes, getVisibleFields } from './sys-butler-fields.js';

const optionLists = {
    Arm_Length: ['Short', 'Medium', 'Long'],
    Axle: ['Single', 'Dual'],
    Ball: ['Gooseneck', '5th Wheel'],
    Beds: ['Flatbed', 'Skirted', 'Tapered'],
    Bed_Feature: ['Straight', 'Tapered', 'Troughed', 'Skirted'],
    Bed_Type: ['Standard', 'Heavy Duty'],
    Cab_Axle: ['60"', '84"', '120"'],
    Headache_Rack: ['Standard', 'Deluxe'],
    Hydraulic_Unit: ['SP', 'DP'],
    Make: ['Ford', 'Chevrolet', 'Ram'],
    Toolboxes: ['Front', 'Rear', 'Across Bed'],
    Spikes: ['BR', 'No Spikes'],
    Spinners: ['SS', 'LS'],
    Years: ['2023', '2024', '2025'],
    ButlerAdapter: ['Adapter 1', 'Adapter 2']
};

function createInput(attr) {
    if (!attr.IsVisible) return '';
    const required = attr.IsRequired ? 'required' : '';
    const disabled = attr.IsLocked ? 'disabled' : '';
    const label = attr.Caption || attr.Name;
    let input = '';
    switch (attr.DisplayType) {
        case 'TypeableDropDown':
            input = `
        <input type="text" id="${attr.Name}" name="${attr.Name}" list="${attr.Name}-list" ${required} ${disabled}>
        <datalist id="${attr.Name}-list">
          ${(optionLists[attr.OptionListID] || []).map(opt => `<option value="${opt}">`).join('')}
        </datalist>
      `;
            break;
        case 'DropDown':
            input = `
        <select id="${attr.Name}" name="${attr.Name}" ${required} ${disabled}>
          <option value="">Select...</option>
          ${(optionLists[attr.OptionListID] || []).map(opt => `<option value="${opt}">${opt}</option>`).join('')}
        </select>
      `;
            break;
        case 'RadioButtonVertical':
        case 'RadioButtonHorizontal':
            input = `
        <div style="display:${attr.DisplayType === 'RadioButtonHorizontal' ? 'flex' : 'block'};gap:1em;align-items:center;">
          <input type="radio" id="${attr.Name}-yes" name="${attr.Name}" value="true" ${required} ${disabled}>
          <label for="${attr.Name}-yes">Yes</label>
          <input type="radio" id="${attr.Name}-no" name="${attr.Name}" value="false" ${disabled}>
          <label for="${attr.Name}-no">No</label>
        </div>
      `;
            break;
        case 'CheckBox':
            input = `<input type="checkbox" id="${attr.Name}" name="${attr.Name}" ${disabled}>`;
            break;
        case 'TextBox':
            input = `<input type="text" id="${attr.Name}" name="${attr.Name}" ${required} ${disabled}>`;
            break;
        default:
            input = `<input type="text" id="${attr.Name}" name="${attr.Name}" ${required} ${disabled}>`;
    }
    return `
    <div class="form-group">
      <label for="${attr.Name}">${label}</label>
      ${input}
    </div>
  `;
}

function renderForm(formData = {}) {
    const container = document.getElementById('form-container');
    container.innerHTML = '';
    const visibleFields = getVisibleFields(formData);
    componentAttributes.forEach((field, idx) => {
        if (!visibleFields[idx]) return;
        container.innerHTML += createInput(field);
    });
}

document.getElementById('form-container').addEventListener('input', handleInput);

// Initial render
renderForm();

document.addEventListener('submit', function (e) {
    if (e.target && e.target.id === 'butler-form') {
        e.preventDefault();
        const data = Object.fromEntries(new FormData(e.target).entries());
        alert('Configuration submitted!\n' + JSON.stringify(data, null, 2));
    }
});
