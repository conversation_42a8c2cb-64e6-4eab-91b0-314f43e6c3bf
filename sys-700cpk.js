// Modular JS for SYS.700CPK Configurator
// This script can be extended to fetch option lists dynamically

import { componentAttributes, getVisibleFields } from './truck-hydraulic-system-fields.js';

const optionLists = {
    Application: ['Utility', 'Agriculture', 'Construction'],
    Years: ['2023', '2024', '2025'],
    Vehicle_Type: ['Truck', 'Van', 'SUV'],
    Make: ['Ford', 'Chevrolet', 'Ram'],
    Engine: ['V8', 'V6', 'Diesel'],
    Pump: ['Hydraulic', 'Electric'],
    Kit: ['Standard', 'Deluxe'],
    Clutch: ['Manual', 'Automatic'],
    Adapter: ['Type A', 'Type B'],
    Product: ['Product 1', 'Product 2'],
    Cab: ['Regular', 'Extended', 'Crew'],
    Chassis: ['Short', 'Long'],
    Valve: ['Valve 1', 'Valve 2'],
    Aux_Hoses: ['Yes', 'No'],
    Control: ['Remote', 'Manual']
};

function createInput(attr) {
    if (!attr.IsVisible) return '';
    const required = attr.IsRequired ? 'required' : '';
    const disabled = attr.IsLocked ? 'disabled' : '';
    const label = attr.Caption || attr.Name;
    let input = '';
    switch (attr.DisplayType) {
        case 'TypeableDropDown':
            input = `
        <input type="text" id="${attr.Name}" name="${attr.Name}" list="${attr.Name}-list" ${required} ${disabled}>
        <datalist id="${attr.Name}-list">
          ${(optionLists[attr.OptionListID] || []).map(opt => `<option value="${opt}">`).join('')}
        </datalist>
      `;
            break;
        case 'RadioButtonVertical':
            input = `
        <div>
          <input type="radio" id="${attr.Name}-yes" name="${attr.Name}" value="true" ${required} ${disabled}>
          <label for="${attr.Name}-yes">Yes</label>
          <input type="radio" id="${attr.Name}-no" name="${attr.Name}" value="false" ${disabled}>
          <label for="${attr.Name}-no">No</label>
        </div>
      `;
            break;
        case 'RadioButtonHorizontal':
            input = `
        <div style="display:flex;gap:1em;align-items:center;">
          <input type="radio" id="${attr.Name}-yes" name="${attr.Name}" value="true" ${required} ${disabled}>
          <label for="${attr.Name}-yes">Yes</label>
          <input type="radio" id="${attr.Name}-no" name="${attr.Name}" value="false" ${disabled}>
          <label for="${attr.Name}-no">No</label>
        </div>
      `;
            break;
        case 'CheckBox':
            input = `<input type="checkbox" id="${attr.Name}" name="${attr.Name}" ${disabled}>`;
            break;
        case 'TextBox':
            input = `<input type="text" id="${attr.Name}" name="${attr.Name}" ${required} ${disabled}>`;
            break;
        default:
            input = `<input type="text" id="${attr.Name}" name="${attr.Name}" ${required} ${disabled}>`;
    }
    return `
    <div class="form-group">
      <label for="${attr.Name}">${label}</label>
      ${input}
    </div>
  `;
}

function renderForm(formData = {}) {
    const container = document.getElementById('config-form'); // PATCH: use correct form id
    container.innerHTML = '';
    const visibleFields = getVisibleFields(formData);
    componentAttributes.forEach((field, idx) => {
        if (!visibleFields[idx]) return;
        container.innerHTML += createInput(field);
    });
}

// Update form on input
function handleInput(e) {
    const formData = {};
    document.querySelectorAll('#config-form [name]').forEach(input => {
        formData[input.name] = input.type === 'checkbox' ? input.checked : input.value;
    });
    renderForm(formData);
}

document.getElementById('config-form').addEventListener('input', handleInput); // PATCH: use correct form id

// Initial render
renderForm();
